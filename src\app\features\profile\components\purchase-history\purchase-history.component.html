<!-- Purchase History Content -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="flex items-center mb-8">
    <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-3xl font-bold text-white mb-2">История покупок</h1>
      <p class="text-gray-300">Управление вашими покупками и платежами</p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Поиск по названию игры</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Введите название игры..."
          class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Статус</label>
        <select
          [(ngModel)]="statusFilter"
          (change)="onStatusFilterChange()"
          class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">Все покупки</option>
          <option value="paid">Оплаченные</option>
          <option value="pending">Ожидают оплаты</option>
          <option value="failed">Ошибка оплаты</option>
        </select>
      </div>

      <!-- Sort -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Сортировка</label>
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="-created_at">Сначала новые</option>
          <option value="created_at">Сначала старые</option>
          <option value="game_title">По названию (А-Я)</option>
          <option value="-game_title">По названию (Я-А)</option>
          <option value="-price">По цене (убывание)</option>
          <option value="price">По цене (возрастание)</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-12">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center">
    <svg class="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <h3 class="text-lg font-medium text-red-400 mb-2">Ошибка загрузки</h3>
    <p class="text-red-300 mb-4">{{ error }}</p>
    <button
      (click)="loadPurchases()"
      class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && purchases.length === 0" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-12 text-center">
    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
    </svg>
    <h3 class="text-xl font-medium text-gray-300 mb-2">Покупки не найдены</h3>
    <p class="text-gray-400 mb-6">
      <span *ngIf="searchTerm || statusFilter !== 'all'">Попробуйте изменить параметры поиска</span>
      <span *ngIf="!searchTerm && statusFilter === 'all'">У вас пока нет покупок</span>
    </p>
    <a href="/games" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md transition-colors inline-block">
      Перейти в каталог игр
    </a>
  </div>

  <!-- Purchases List -->
  <div *ngIf="!loading && !error && purchases.length > 0" class="space-y-4">
    <!-- Purchase Cards -->
    <div 
      *ngFor="let purchase of purchases" 
      class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6 hover:border-slate-500/60 transition-all duration-300"
    >
      <div class="flex items-center justify-between">
        <!-- Purchase Info -->
        <div class="flex-1">
          <div class="flex items-center mb-2">
            <h3 class="text-lg font-medium text-white mr-4">{{ purchase.game_title }}</h3>
            <span 
              class="px-3 py-1 rounded-full text-xs font-medium border"
              [ngClass]="getStatusClass(purchase.status)"
            >
              {{ getStatusText(purchase.status) }}
            </span>
          </div>
          <div class="text-sm text-gray-400 space-y-1">
            <p>Дата покупки: {{ formatDate(purchase.created_at) }}</p>
            <p>Цена: {{ formatPrice(purchase.price) }}</p>
            <p>ID покупки: #{{ purchase.id }}</p>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center space-x-3">
          <!-- Payment Button for Pending Purchases -->
          <button
            *ngIf="purchase.status === 'pending'"
            (click)="payForPurchase(purchase)"
            [disabled]="isPaymentLoading(purchase.id)"
            class="bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white px-4 py-2 rounded-md transition-colors flex items-center space-x-2"
          >
            <svg *ngIf="isPaymentLoading(purchase.id)" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg *ngIf="!isPaymentLoading(purchase.id)" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path>
            </svg>
            <span>{{ isPaymentLoading(purchase.id) ? 'Обработка...' : 'Оплатить' }}</span>
          </button>

          <!-- Paid Status -->
          <div *ngIf="purchase.status === 'paid'" class="flex items-center text-green-400">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm font-medium">Оплачено</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="getTotalPages() > 1" class="flex justify-center items-center space-x-2 mt-8">
      <!-- Previous Button -->
      <button
        (click)="previousPage()"
        [disabled]="!hasPrevious"
        class="px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-gray-300 hover:bg-slate-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <!-- Page Numbers -->
      <button
        *ngFor="let page of getPageNumbers()"
        (click)="goToPage(page)"
        [ngClass]="{'bg-blue-600 text-white': page === currentPage, 'bg-slate-700/50 text-gray-300 hover:bg-slate-600/50': page !== currentPage}"
        class="px-3 py-2 border border-slate-600/50 rounded-md transition-colors"
      >
        {{ page }}
      </button>

      <!-- Next Button -->
      <button
        (click)="nextPage()"
        [disabled]="!hasNext"
        class="px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-gray-300 hover:bg-slate-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>

    <!-- Results Info -->
    <div class="text-center text-gray-400 text-sm mt-4">
      Показано {{ purchases.length }} из {{ totalPurchases }} покупок
    </div>
  </div>
</div>
