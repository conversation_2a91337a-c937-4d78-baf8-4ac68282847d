import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GameService } from '../../core/services/game.service';
import { CartService } from '../../core/services/cart.service';
import { AuthService } from '../../core/services/auth.service';
import { ModalService } from '../../core/services/modal.service';
import { Game } from '../../core/models/game.model';

@Component({
  selector: 'app-main',
  standalone: false,
  templateUrl: './main.html',
  styleUrl: './main.css'
})
export class Main implements OnInit {
  // Featured games for main page (limit to 3)
  featuredGames: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  constructor(
    private gameService: GameService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadFeaturedGames();
  }

  /**
   * Load featured games for the main page (first 3 games)
   */
  loadFeaturedGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames(undefined, 1, 3).subscribe({
      next: (response) => {
        this.featuredGames = response.results;
        this.gamesLoading = false;
      },
      error: (error) => {
        console.error('Error loading featured games:', error);
        this.gamesError = 'Ошибка загрузки игр';
        this.gamesLoading = false;
      }
    });
  }

  /**
   * Navigate to games catalog page
   */
  viewAllGames(): void {
    this.router.navigate(['/games']);
  }

  /**
   * Navigate to game detail page
   */
  viewGameDetails(gameId: number): void {
    this.router.navigate(['/games', gameId]);
  }

  /**
   * Add game to cart
   */
  addToCart(game: Game, event: Event): void {
    event.stopPropagation(); // Prevent navigation to game details

    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Вход в систему', 'Для добавления в корзину необходимо войти в систему');
      return;
    }

    if (this.isInLibrary(game)) {
      this.modalService.error('Игра уже в библиотеке', 'Эта игра уже есть в вашей библиотеке');
      return;
    }

    if (this.isInCart(game)) {
      return;
    }

    this.cartService.addToCart(game).subscribe({
      next: () => {
        // Update the local game object to reflect the cart status immediately
        game.is_in_cart = true;
        this.modalService.success('Успех', 'Игра добавлена в корзину');
      },
      error: (error) => {
        console.error('Error adding to cart:', error);
        this.modalService.error('Ошибка', 'Ошибка при добавлении в корзину');
      }
    });
  }

  /**
   * Check if game is already in cart
   */
  isInCart(game: Game): boolean {
    return game.is_in_cart || false;
  }

  /**
   * Check if game is already in library
   */
  isInLibrary(game: Game): boolean {
    return game.is_in_library || false;
  }

  /**
   * Format price for display
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('ru-KZ', {
      style: 'currency',
      currency: 'KZT',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numPrice);
  }
}
