<!-- Profile Page Container -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900">
  <div class="flex h-screen">
    <!-- Left Sidebar -->
    <div class="w-80 bg-gradient-to-b from-slate-800/95 to-blue-900/95 backdrop-blur-sm border-r border-slate-600/30 flex flex-col">
      <!-- Logo Section -->
      <div class="p-4 border-b border-slate-600/30">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-white font-bold text-lg">TOY FOR TOI</h1>
            <p class="text-blue-300 text-sm">{{ userProfile?.email }}</p>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="flex-1 p-4 space-y-6">
        <!-- My Games Section -->
        <div class="mb-6">
          <h2 class="text-white font-medium text-base mb-3">Мои игры</h2>
          <div class="space-y-1">
            <div (click)="setActiveSection('library')" [ngClass]="{'bg-slate-700/70': activeSection === 'library'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Библиотека
            </div> 
          </div>
        </div>

        <!-- Account Section -->
        <div class="mb-6">
          <h2 class="text-white font-medium text-base mb-3">Учетная запись</h2>
          <div class="space-y-1">
            <div (click)="setActiveSection('profile')" [ngClass]="{'bg-slate-700/70': activeSection === 'profile'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Параметры профиля
            </div> 
          </div>
        </div>

        <!-- Store Section -->
        <div class="mb-6">
          <h2 class="text-white font-medium text-base mb-3">Магазин</h2>
          <div class="space-y-1">
            <a href="/games" class="block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50">
              Каталог игр
            </a>
            <a href="/cart" class="block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50">
              Корзина
            </a>
            <div (click)="setActiveSection('purchases')" [ngClass]="{'bg-slate-700/70': activeSection === 'purchases'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              История покупок
            </div>
          </div>
        </div>

        <!-- Administration Section (Only for Staff) -->
        <div *ngIf="userProfile?.is_staff" class="mb-6">
          <h2 class="text-white font-medium text-base mb-3">Администрирование</h2>
          <div class="space-y-1">
            <div (click)="setActiveSection('users')" [ngClass]="{'bg-slate-700/70': activeSection === 'users'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Управление пользователями
            </div>
            <div (click)="setActiveSection('games')" [ngClass]="{'bg-slate-700/70': activeSection === 'games'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Управление играми
            </div>
            <div (click)="setActiveSection('admin-library')" [ngClass]="{'bg-slate-700/70': activeSection === 'admin-library'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Управление библиотекой
            </div>
            <div (click)="setActiveSection('game-keys')" [ngClass]="{'bg-slate-700/70': activeSection === 'game-keys'}" class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1.5 px-2.5 rounded text-sm hover:bg-slate-700/50 cursor-pointer">
              Управление ключами игр
            </div>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-4 border-t border-slate-600/30">
        <button (click)="onLogout()" class="w-full bg-slate-700/60 hover:bg-slate-600/70 text-gray-300 hover:text-white font-normal py-2.5 px-3 rounded-md border border-slate-600/40 hover:border-slate-500/60 transition-all text-sm">
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Выйти
        </button>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 p-8 overflow-y-auto">
      <!-- Loading State -->
      <app-loading-spinner
        *ngIf="isLoading"
        [overlay]="true">
      </app-loading-spinner>

      <!-- Error State -->
      <div *ngIf="errorMessage && !isLoading" class="flex items-center justify-center h-64">
        <div class="text-center">
          <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md">
            <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
            <p class="text-red-200 mb-4">{{ errorMessage }}</p>
            <button (click)="loadUserProfile()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
              Попробовать снова
            </button>
          </div>
        </div>
      </div>

      <!-- Profile Content -->
      <div *ngIf="userProfile && !isLoading">
        <!-- Profile Settings Component -->
        <app-profile-settings
          *ngIf="activeSection === 'profile'"
          [userProfile]="userProfile"
          (refreshProfile)="refreshProfile()">
        </app-profile-settings>

        <!-- User Library Component -->
        <app-user-library
          *ngIf="activeSection === 'library'">
        </app-user-library>

        <!-- Purchase History Component -->
        <app-purchase-history
          *ngIf="activeSection === 'purchases'">
        </app-purchase-history>

        <!-- Users Management Component -->
        <app-users-management
          *ngIf="activeSection === 'users'">
        </app-users-management>

        <!-- Games Management Component -->
        <app-games-management
          *ngIf="activeSection === 'games'">
        </app-games-management>

        <!-- Library Management Component -->
        <app-library-management
          *ngIf="activeSection === 'admin-library'">
        </app-library-management>

        <!-- Game Keys Management Component -->
        <app-game-keys-management
          *ngIf="activeSection === 'game-keys'">
        </app-game-keys-management>
      </div>
    </div>
  </div>
</div>