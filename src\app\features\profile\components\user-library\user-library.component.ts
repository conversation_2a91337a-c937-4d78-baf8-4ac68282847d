import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { LibraryService } from '../../../../core/services/library.service';
import { ModalService } from '../../../../core/services/modal.service';
import { LibraryItem, LibraryFilters } from '../../../../core/models/library.model';

@Component({
  selector: 'app-user-library',
  standalone: false,
  templateUrl: './user-library.component.html',
  styleUrl: './user-library.component.css'
})
export class UserLibraryComponent implements OnInit, OnDestroy {
  libraryItems: LibraryItem[] = [];
  loading = false;
  error = '';

  // Pagination
  totalItems = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-added_at';
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;

  constructor(
    private libraryService: LibraryService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadLibrary();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadLibrary();
    });
  }

  loadLibrary(): void {
    this.loading = true;
    this.error = '';

    const filters: LibraryFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.libraryService.getLibrary(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.libraryItems = response.results;
        this.totalItems = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить библиотеку';
        this.loading = false;
        this.modalService.error('Ошибка', this.error);
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadLibrary();
  }

  goToGameDetail(gameId: number): void {
    this.router.navigate(['/games', gameId]);
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.loadLibrary();
    }
  }

  nextPage(): void {
    if (this.hasNext) {
      this.goToPage(this.currentPage + 1);
    }
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.goToPage(this.currentPage - 1);
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
