import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { GameService } from '../../core/services/game.service';
import { CartService } from '../../core/services/cart.service';
import { ModalService } from '../../core/services/modal.service';
import { Game, GameFilters } from '../../core/models/game.model';

@Component({
  selector: 'app-games-catalog',
  standalone: false,
  templateUrl: './games-catalog.component.html',
  styleUrl: './games-catalog.component.css'
})
export class GamesCatalogComponent implements OnInit, OnDestroy {
  games: Game[] = [];
  loading = false;
  error = '';

  // Pagination
  totalGames = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-created_at';
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;

  // Cart
  cartItemCount = 0;
  private cartSubscription?: Subscription;
  private cartChangesSubscription?: Subscription;

  constructor(
    private gameService: GameService,
    private cartService: CartService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadGames();
    this.setupSearch();
    this.setupCartSubscription();
    this.setupCartChangesSubscription();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
    this.cartSubscription?.unsubscribe();
    this.cartChangesSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadGames();
    });
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cartItemCount = cart.total_items;
    });
  }

  private setupCartChangesSubscription(): void {
    this.cartChangesSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the corresponding game's is_in_cart status
      const game = this.games.find(g => g.id === change.gameId);
      if (game) {
        game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGames(): void {
    this.loading = true;
    this.error = '';

    const filters: GameFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.gameService.getGames(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.games = response.results;
        this.totalGames = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить игры';
        this.loading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadGames();
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.currentPage--;
      this.loadGames();
    }
  }

  nextPage(): void {
    if (this.hasNext) {
      this.currentPage++;
      this.loadGames();
    }
  }

  viewGameDetails(gameId: number): void {
    this.router.navigate(['/games', gameId]);
  }

  addToCart(game: Game, event: Event): void {
    event.stopPropagation(); // Prevent navigation to details

    if (this.isInLibrary(game)) {
      this.modalService.error('Игра уже в библиотеке', 'Эта игра уже есть в вашей библиотеке');
      return;
    }

    this.cartService.addToCart(game).subscribe({
      next: () => {
        // Success - cart will be automatically updated via subscription
        // Update the local game object to reflect the cart status immediately
        game.is_in_cart = true;
        console.log('Game added to cart successfully');
      },
      error: (error) => {
        console.error('Error adding game to cart:', error.message);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
      }
    });
  }

  isInCart(game: Game): boolean {
    // Use the is_in_cart field from the API response if available (when user is authenticated)
    // Fall back to cart service check if not available (when user is not authenticated)
    return game.is_in_cart !== undefined ? game.is_in_cart : this.cartService.isInCart(game.id);
  }

  isInLibrary(game: Game): boolean {
    return game.is_in_library || false;
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  getTotalPages(): number {
    return Math.ceil(this.totalGames / this.pageSize);
  }
}
