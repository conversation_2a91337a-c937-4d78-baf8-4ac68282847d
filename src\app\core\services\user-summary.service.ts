import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { tap, catchError, delay } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface UserSummary {
  cart_count: number;
  library_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class UserSummaryService {
  private apiUrl = `${environment.apiUrl}/api/user/summary`;
  private useMockData = true; // Set to false when real API is available

  // BehaviorSubject to store and emit the current summary
  private summarySubject = new BehaviorSubject<UserSummary | null>(null);
  public summary$ = this.summarySubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get user summary (cart count and library count)
   */
  getUserSummary(): Observable<UserSummary> {
    if (this.useMockData) {
      return this.getMockSummary();
    }

    return this.http.get<UserSummary>(`${this.apiUrl}/`).pipe(
      tap(summary => {
        // Update the BehaviorSubject with the new summary
        this.summarySubject.next(summary);
      }),
      catchError(error => {
        console.error('Error fetching user summary:', error);
        // Reset summary on error
        this.summarySubject.next(null);
        throw error;
      })
    );
  }

  /**
   * Mock data for testing purposes
   */
  private getMockSummary(): Observable<UserSummary> {
    const mockSummary: UserSummary = {
      cart_count: 3,
      library_count: 12
    };

    return of(mockSummary).pipe(
      delay(500), // Simulate API delay
      tap(summary => {
        this.summarySubject.next(summary);
      })
    );
  }

  /**
   * Refresh the user summary
   */
  refreshSummary(): void {
    this.getUserSummary().subscribe({
      next: () => {
        // Summary is automatically updated via tap operator
      },
      error: (error) => {
        console.error('Failed to refresh user summary:', error);
      }
    });
  }

  /**
   * Get the current summary value without making an API call
   */
  getCurrentSummary(): UserSummary | null {
    return this.summarySubject.value;
  }

  /**
   * Clear the summary (useful for logout)
   */
  clearSummary(): void {
    this.summarySubject.next(null);
  }
}
