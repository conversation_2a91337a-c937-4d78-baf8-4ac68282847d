<!-- Library Management Component -->
<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h2 class="text-2xl font-bold text-white mb-2">Управление библиотекой</h2>
      <p class="text-gray-400">Добавление игр в библиотеки пользователей</p>
    </div>
    
    <!-- Add Game Button -->
    <button
      (click)="toggleAddForm()"
      class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 font-medium text-sm"
    >
      {{ showAddForm ? 'Отменить' : 'Добавить игру' }}
    </button>
  </div>

  <!-- Add Game Form -->
  <div *ngIf="showAddForm" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Добавить игру в библиотеку</h3>
    
    <!-- Error Message -->
    <div *ngIf="addError" class="bg-red-900/20 border border-red-500/50 rounded-lg p-3 mb-4">
      <p class="text-red-400 text-sm">{{ addError }}</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      
      <!-- Game Selection -->
      <div class="space-y-4">
        <h4 class="text-white font-medium">Выберите игру</h4>
        
        <!-- Game Search -->
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="gameSearchTerm"
            (input)="onGameSearchChange()"
            placeholder="Поиск игр..."
            class="w-full bg-slate-700/50 border border-slate-600/50 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
          <svg class="absolute right-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>

        <!-- Games List -->
        <div class="bg-slate-700/30 border border-slate-600/30 rounded-lg max-h-64 overflow-y-auto">
          <div *ngIf="gamesLoading" class="p-4 text-center">
            <app-loading-spinner></app-loading-spinner>
          </div>
          
          <div *ngIf="gamesError && !gamesLoading" class="p-4 text-red-400 text-sm text-center">
            {{ gamesError }}
          </div>
          
          <div *ngIf="!gamesLoading && !gamesError && games.length === 0" class="p-4 text-gray-400 text-sm text-center">
            Игры не найдены
          </div>
          
          <div *ngIf="!gamesLoading && !gamesError && games.length > 0" class="divide-y divide-slate-600/30">
            <div
              *ngFor="let game of games"
              (click)="selectGame(game.id)"
              [class]="selectedGameId === game.id ? 
                'p-3 cursor-pointer hover:bg-slate-600/30 bg-blue-600/20 border-l-4 border-blue-500' : 
                'p-3 cursor-pointer hover:bg-slate-600/30'"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <h5 class="text-white font-medium text-sm truncate">{{ game.title }}</h5>
                  <p class="text-gray-400 text-xs truncate">{{ game.description }}</p>
                </div>
                <span class="text-blue-400 text-sm font-medium ml-2">{{ game.price }}₽</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Selected Game Display -->
        <div *ngIf="selectedGameId" class="bg-blue-900/20 border border-blue-500/50 rounded-lg p-3">
          <p class="text-blue-400 text-sm font-medium">Выбранная игра:</p>
          <p class="text-white text-sm">{{ getSelectedGame()?.title }}</p>
        </div>
      </div>

      <!-- User Selection -->
      <div class="space-y-4">
        <h4 class="text-white font-medium">Выберите пользователя</h4>
        
        <!-- User Search -->
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="userSearchTerm"
            (input)="onUserSearchChange()"
            placeholder="Поиск пользователей..."
            class="w-full bg-slate-700/50 border border-slate-600/50 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
          <svg class="absolute right-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>

        <!-- Users List -->
        <div class="bg-slate-700/30 border border-slate-600/30 rounded-lg max-h-64 overflow-y-auto">
          <div *ngIf="usersLoading" class="p-4 text-center">
            <app-loading-spinner></app-loading-spinner>
          </div>
          
          <div *ngIf="usersError && !usersLoading" class="p-4 text-red-400 text-sm text-center">
            {{ usersError }}
          </div>
          
          <div *ngIf="!usersLoading && !usersError && users.length === 0" class="p-4 text-gray-400 text-sm text-center">
            Пользователи не найдены
          </div>
          
          <div *ngIf="!usersLoading && !usersError && users.length > 0" class="divide-y divide-slate-600/30">
            <div
              *ngFor="let user of users"
              (click)="selectUser(user.id)"
              [class]="selectedUserId === user.id ? 
                'p-3 cursor-pointer hover:bg-slate-600/30 bg-blue-600/20 border-l-4 border-blue-500' : 
                'p-3 cursor-pointer hover:bg-slate-600/30'"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <h5 class="text-white font-medium text-sm truncate">{{ user.email }}</h5>
                  <p class="text-gray-400 text-xs">{{ user.username }}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <span *ngIf="user.is_staff" class="bg-purple-600 text-white text-xs px-2 py-1 rounded">Staff</span>
                  <span [class]="user.is_active ? 'bg-green-600 text-white text-xs px-2 py-1 rounded' : 'bg-red-600 text-white text-xs px-2 py-1 rounded'">
                    {{ user.is_active ? 'Активен' : 'Неактивен' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Selected User Display -->
        <div *ngIf="selectedUserId" class="bg-blue-900/20 border border-blue-500/50 rounded-lg p-3">
          <p class="text-blue-400 text-sm font-medium">Выбранный пользователь:</p>
          <p class="text-white text-sm">{{ getSelectedUser()?.email }}</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-slate-600/30">
      <button
        (click)="toggleAddForm()"
        class="px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm"
      >
        Отменить
      </button>
      <button
        (click)="addGameToLibrary()"
        [disabled]="!selectedGameId || !selectedUserId || addLoading"
        class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
      >
        <span *ngIf="addLoading" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Добавление...
        </span>
        <span *ngIf="!addLoading">Добавить в библиотеку</span>
      </button>
    </div>
  </div>

  <!-- Instructions -->
  <div *ngIf="!showAddForm" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-white mb-3">Инструкции</h3>
    <div class="space-y-2 text-gray-300 text-sm">
      <p>• Используйте кнопку "Добавить игру" для ручного добавления игр в библиотеки пользователей</p>
      <p>• Выберите игру из списка доступных игр</p>
      <p>• Выберите пользователя, которому нужно добавить игру</p>
      <p>• Подтвердите добавление игры в библиотеку</p>
      <p>• Игры также автоматически добавляются в библиотеку после успешной оплаты</p>
    </div>
  </div>
</div>
