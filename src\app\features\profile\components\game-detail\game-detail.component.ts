import { Component, OnInit, OnChanges, Input, Output, EventEmitter } from '@angular/core';
import { GameService } from '../../../../core/services/game.service';
import { Game, UpdateGameRequest } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrls: ['./game-detail.component.css']
})
export class GameDetailComponent implements OnInit, OnChanges {
  @Input() gameId: number | null = null;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() gameUpdated = new EventEmitter<Game>();
  @Output() gameDeleted = new EventEmitter<number>();

  game: Game | null = null;
  loading = false;
  error = '';

  // Edit mode
  isEditing = false;
  editLoading = false;
  editError = '';
  editGame: UpdateGameRequest = {};
  selectedCoverImage: File | null = null;

  // Delete confirmation
  showDeleteConfirm = false;
  deleteLoading = false;

  constructor(private gameService: GameService) {}

  ngOnInit(): void {
    if (this.gameId && this.isVisible) {
      this.loadGame();
    }
  }

  ngOnChanges(): void {
    if (this.gameId && this.isVisible && !this.game) {
      this.loadGame();
    }
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.initializeEditForm();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load game details';
        this.loading = false;
      }
    });
  }

  initializeEditForm(): void {
    if (!this.game) return;

    this.editGame = {
      title: this.game.title,
      subtitle: this.game.subtitle || '',
      description: this.game.description,
      how_to_play: this.game.how_to_play || '',
      target_audience: this.game.target_audience || '',
      requires_device: this.game.requires_device,
      price: this.game.price,
      trial_available: this.game.trial_available,
      system_requirements: this.game.system_requirements || '',
      required_equipment: this.game.required_equipment || ''
    };
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing) {
      this.initializeEditForm();
      this.selectedCoverImage = null;
      this.editError = '';
    }
  }

  onCoverImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.editError = 'Пожалуйста, выберите файл изображения';
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.editError = 'Размер файла не должен превышать 5MB';
        return;
      }
      
      this.selectedCoverImage = file;
      this.editError = '';
    }
  }

  saveChanges(): void {
    if (!this.game || !this.gameId) return;

    // Validate required fields
    if (!this.editGame.title?.trim()) {
      this.editError = 'Название игры обязательно';
      return;
    }
    
    if (!this.editGame.description?.trim()) {
      this.editError = 'Описание игры обязательно';
      return;
    }
    
    if (!this.editGame.price?.trim()) {
      this.editError = 'Цена игры обязательна';
      return;
    }

    // Validate price format
    const priceNum = parseFloat(this.editGame.price);
    if (isNaN(priceNum) || priceNum < 0) {
      this.editError = 'Введите корректную цену (должна быть больше или равна 0)';
      return;
    }

    this.editLoading = true;
    this.editError = '';

    // Prepare update data
    const updateData: UpdateGameRequest = {
      ...this.editGame,
      price: priceNum.toFixed(2)
    };

    // Add cover image if selected
    if (this.selectedCoverImage) {
      updateData.cover_image = this.selectedCoverImage;
    }

    this.gameService.updateGame(this.gameId, updateData).subscribe({
      next: (updatedGame) => {
        this.game = updatedGame;
        this.isEditing = false;
        this.editLoading = false;
        this.selectedCoverImage = null;
        this.gameUpdated.emit(updatedGame);
        alert('Игра успешно обновлена!');
      },
      error: (error) => {
        this.editLoading = false;
        
        // Handle validation errors
        if (error.error && typeof error.error === 'object') {
          const errors = error.error;
          if (errors.title) {
            this.editError = 'Название: ' + errors.title.join(', ');
          } else if (errors.description) {
            this.editError = 'Описание: ' + errors.description.join(', ');
          } else if (errors.price) {
            this.editError = 'Цена: ' + errors.price.join(', ');
          } else if (errors.cover_image) {
            this.editError = 'Изображение: ' + errors.cover_image.join(', ');
          } else if (errors.non_field_errors) {
            this.editError = errors.non_field_errors.join(', ');
          } else {
            this.editError = 'Ошибка при обновлении игры';
          }
        } else {
          this.editError = error.message || 'Ошибка при обновлении игры';
        }
      }
    });
  }

  confirmDelete(): void {
    this.showDeleteConfirm = true;
  }

  cancelDelete(): void {
    this.showDeleteConfirm = false;
  }

  deleteGame(): void {
    if (!this.gameId) return;

    this.deleteLoading = true;

    this.gameService.deleteGame(this.gameId).subscribe({
      next: () => {
        this.deleteLoading = false;
        this.showDeleteConfirm = false;
        this.gameDeleted.emit(this.gameId!);
        this.closeModal();
        alert('Игра успешно удалена!');
      },
      error: (error) => {
        this.deleteLoading = false;
        alert('Ошибка при удалении игры: ' + error.message);
      }
    });
  }

  closeModal(): void {
    this.isEditing = false;
    this.showDeleteConfirm = false;
    this.editError = '';
    this.selectedCoverImage = null;
    this.close.emit();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPrice(price: string): string {
    const num = parseFloat(price);
    return num.toLocaleString('ru-RU', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
}
