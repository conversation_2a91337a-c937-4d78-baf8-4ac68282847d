<!-- User Library Component -->
<div class="space-y-6">
  <!-- Header -->
  <div class="mb-6">
    <h2 class="text-2xl font-bold text-white mb-2">Моя библиотека</h2>
    <p class="text-gray-400">Ваши приобретенные игры</p>
  </div>

  <!-- Search and Filter Controls -->
  <div class="bg-slate-800/40 backdrop-blur-sm border border-slate-600/50 rounded-lg p-4">
    <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
      
      <!-- Search Input -->
      <div class="flex-1 max-w-md">
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Поиск игр в библиотеке..."
            class="w-full bg-slate-700/50 border border-slate-600/50 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
          <svg class="absolute right-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>

      <!-- Sort Dropdown -->
      <div class="flex items-center gap-4">
        <label class="text-gray-300 text-sm">Сортировка:</label>
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="bg-slate-700/50 border border-slate-600/50 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="-added_at">Недавно добавленные</option>
          <option value="added_at">Давно добавленные</option>
          <option value="game__title">По названию (А-Я)</option>
          <option value="-game__title">По названию (Я-А)</option>
          <option value="game__price">По цене (возрастание)</option>
          <option value="-game__price">По цене (убывание)</option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="text-gray-300 text-sm">
        Найдено: {{ totalItems }} {{ totalItems === 1 ? 'игра' : totalItems < 5 ? 'игры' : 'игр' }}
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div>
        <h3 class="text-red-400 font-medium text-sm">Ошибка загрузки</h3>
        <p class="text-red-300 text-sm">{{ error }}</p>
      </div>
    </div>
    <button
      (click)="loadLibrary()"
      class="mt-3 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && libraryItems.length === 0" class="text-center py-8">
    <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
      <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
      <h3 class="text-lg font-medium text-white mb-2">Ваша библиотека пуста</h3>
      <p class="text-gray-400 mb-4">У вас пока нет приобретенных игр</p>
      <a
        href="/games"
        class="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 font-medium text-sm"
      >
        Перейти к каталогу игр
      </a>
    </div>
  </div>

  <!-- Library Grid -->
  <div *ngIf="!loading && !error && libraryItems.length > 0" class="space-y-4">
    <!-- Games Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div 
        *ngFor="let item of libraryItems" 
        class="bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer group"
        (click)="goToGameDetail(item.game.id)"
      >
        <!-- Game Cover Image -->
        <div class="h-32 bg-gradient-to-br from-slate-700 to-slate-800 relative overflow-hidden">
          <img
            *ngIf="item.game.cover_image"
            [src]="item.game.cover_image"
            [alt]="item.game.title"
            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          >
          <div *ngIf="!item.game.cover_image" class="w-full h-full flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          
          <!-- Owned Badge -->
          <div class="absolute top-2 right-2">
            <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full font-medium">
              В библиотеке
            </span>
          </div>
        </div>

        <!-- Game Info -->
        <div class="p-3">
          <h3 class="text-white font-semibold text-sm mb-1 line-clamp-1">{{ item.game.title }}</h3>
          <p *ngIf="item.game.subtitle" class="text-gray-400 text-xs mb-1 line-clamp-1">{{ item.game.subtitle }}</p>
          <p class="text-gray-300 text-xs mb-2 line-clamp-2">{{ item.game.description }}</p>
          
          <!-- Added Date -->
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-400">{{ formatDate(item.added_at) }}</span>
            <span class="text-blue-400 font-medium">{{ item.game.price }}₽</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="getTotalPages() > 1" class="flex justify-center items-center space-x-2 mt-6">
      <!-- Previous Button -->
      <button
        (click)="previousPage()"
        [disabled]="!hasPrevious"
        class="px-3 py-2 rounded-lg bg-slate-700/50 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600/50 transition-colors text-sm"
      >
        ←
      </button>

      <!-- Page Numbers -->
      <button
        *ngFor="let page of getPageNumbers()"
        (click)="goToPage(page)"
        [class]="page === currentPage ? 
          'px-3 py-2 rounded-lg bg-blue-600 text-white text-sm' : 
          'px-3 py-2 rounded-lg bg-slate-700/50 text-white hover:bg-slate-600/50 transition-colors text-sm'"
      >
        {{ page }}
      </button>

      <!-- Next Button -->
      <button
        (click)="nextPage()"
        [disabled]="!hasNext"
        class="px-3 py-2 rounded-lg bg-slate-700/50 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600/50 transition-colors text-sm"
      >
        →
      </button>
    </div>
  </div>
</div>
