import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { GamesCatalogComponent } from './games-catalog.component';
import { GameDetailComponent } from './game-detail.component';

@NgModule({
  declarations: [
    GamesCatalogComponent,
    GameDetailComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    SharedModule
  ]
})
export class GamesModule { }
