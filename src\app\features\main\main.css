/* Custom animations and effects for the layered site */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

/* Enhanced floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-25px) rotate(-1deg);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

/* Flying animation for edge-positioned decorative images */
@keyframes fly-left {
  0%, 100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateX(-20px) translateY(-15px) rotate(2deg);
  }
  50% {
    transform: translateX(-10px) translateY(-30px) rotate(-1deg);
  }
  75% {
    transform: translateX(-25px) translateY(-10px) rotate(1deg);
  }
}

@keyframes fly-right {
  0%, 100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateX(20px) translateY(-15px) rotate(-2deg);
  }
  50% {
    transform: translateX(10px) translateY(-30px) rotate(1deg);
  }
  75% {
    transform: translateX(25px) translateY(-10px) rotate(-1deg);
  }
}

@keyframes fly-bottom {
  0%, 100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateX(-10px) translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateX(15px) translateY(-35px) rotate(-2deg);
  }
}

.animate-fly-left {
  animation: fly-left 12s ease-in-out infinite;
}

.animate-fly-right {
  animation: fly-right 10s ease-in-out infinite;
}

.animate-fly-bottom {
  animation: fly-bottom 14s ease-in-out infinite;
}

/* Gentle sparkle animation for highlights */
@keyframes sparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

.animate-sparkle {
  animation: sparkle 6s ease-in-out infinite;
}

/* Gentle glow pulse for highlights */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(64, 163, 255, 0.3);
    opacity: 0.6;
  }
  50% {
    box-shadow: 0 0 25px rgba(64, 163, 255, 0.6);
    opacity: 1;
  }
}

.animate-glow-pulse {
  animation: glow-pulse 8s ease-in-out infinite;
}

/* Pulse glow animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
  }
}

/* Enhanced glow effect for cards */
.glow-on-hover {
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.4);
  transform: translateY(-5px);
}

/* Custom backdrop blur for better browser support */
.backdrop-blur-custom {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Beautiful gradient backgrounds */
.gradient-bg-1 {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg,
    #a8edea 0%,
    #fed6e3 25%,
    #d299c2 50%,
    #fef9d7 75%,
    #667eea 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg,
    #ff9a9e 0%,
    #fecfef 25%,
    #fecfef 50%,
    #ff9a9e 75%,
    #a8edea 100%);
}

/* Animated gradient background */
.animated-gradient {
  background: linear-gradient(-45deg,
    #3b82f6 0%,
    #7c3aed 15%,
    #1e1b4b 30%,
    #764ba2 45%,
    #000000 60%,
    #581c87 75%,
    #1e40af 90%,
    #000000 100%);
  background-size: 400% 400%;
  animation: gradient-shift 25s ease infinite;
}

/* Ensure proper layering */
.layer-base {
  position: relative;
  z-index: 1;
}

.layer-content {
  position: relative;
  z-index: 10;
}

.layer-overlay {
  position: relative;
  z-index: 20;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .animate-float {
    animation-duration: 6s;
  }

  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Mobile-specific adjustments */
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .section-title {
    font-size: 2rem;
  }

  /* Reduce floating element sizes on mobile */
  .floating-element {
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  /* Further reduce spacing on very small screens */
  .section-padding {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-title {
    font-size: 3.5rem;
  }

  .section-title {
    font-size: 2.5rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .hero-title {
    font-size: 5rem;
  }

  .section-title {
    font-size: 4rem;
  }

  /* Enhanced floating animations on larger screens */
  .animate-float {
    animation-duration: 10s;
  }
}

/* Smooth section transitions */
.section-transition {
  position: relative;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));
  pointer-events: none;
}

.section-transition::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, transparent, rgba(0, 0, 0, 0.1));
  pointer-events: none;
}

/* Subtle content overlays for better readability */
.content-overlay {
  position: relative;
}

.content-overlay::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.05);
  border-radius: inherit;
  pointer-events: none;
}

/* Games section specific styles */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Game card specific hover effects */
.game-card-main {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-card-main:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}