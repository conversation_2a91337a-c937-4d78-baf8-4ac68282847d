/* Game Detail Specific Styles */

/* Image hover effects */
.game-cover img {
  transition: transform 0.3s ease;
}

.game-cover:hover img {
  transform: scale(1.05);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
}

/* Back button hover */
.back-button {
  transition: all 0.2s ease;
}

.back-button:hover {
  transform: translateX(-4px);
}

/* Section cards */
.detail-section {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.detail-section:hover {
  border-color: rgba(148, 163, 184, 0.3);
  transform: translateY(-2px);
}

/* Gallery image hover effects */
.gallery-image {
  transition: transform 0.3s ease;
  cursor: pointer;
}

.gallery-image:hover {
  transform: scale(1.05);
}

/* Badge animations */
.badge {
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Price highlight */
.price-text {
  text-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade in animation for content */
.game-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .game-header {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .game-title {
    font-size: 2rem;
    line-height: 2.5rem;
  }
  
  .game-subtitle {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .detail-sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 640px) {
  .game-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .action-button {
    width: 100%;
    text-align: center;
  }
  
  .price-badges {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Text content styling */
.description-text {
  line-height: 1.7;
  font-size: 1rem;
}

.section-title {
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 2px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  border-radius: 1px;
}

/* Error state styling */
.error-state {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Disabled button styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar for long content */
.detail-section::-webkit-scrollbar {
  width: 8px;
}

.detail-section::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.detail-section::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.detail-section::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Image loading placeholder */
.image-placeholder {
  background: linear-gradient(45deg, #374151, #4b5563);
  background-size: 400% 400%;
  animation: gradientShift 2s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
