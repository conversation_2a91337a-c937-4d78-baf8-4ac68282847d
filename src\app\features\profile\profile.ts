import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { GameService } from '../../core/services/game.service';
import { UserService } from '../../core/services/user.service';
import { UserSummaryService, UserSummary } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { Router, ActivatedRoute } from '@angular/router';
import { Game } from '../../core/models/game.model';
import { User, UserFilters } from '../../core/models/user.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnInit, OnDestroy {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // User summary data
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;

  // Section management (both user and admin sections)
  activeSection: 'profile' | 'library' | 'purchases' | 'users' | 'games' | 'admin-library' | 'game-keys' = 'profile';

  // Games management
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Users management
  users: User[] = [];
  usersLoading = false;
  usersError = '';
  totalUsers = 0;
  currentPage = 1;
  pageSize = 10;

  // Search and filtering
  searchTerm = '';
  selectedRole: 'all' | 'staff' | 'user' | 'superuser' = 'all';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  sortBy: 'date_joined' | '-date_joined' | 'email' | '-email' | 'username' | '-username' = '-date_joined';

  constructor(
    private authService: AuthService,
    private gameService: GameService,
    private userService: UserService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadUserSummary();

    // Check for fragment navigation (e.g., from checkout)
    this.route.fragment.subscribe(fragment => {
      if (fragment === 'purchases') {
        this.activeSection = 'purchases';
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.summarySubscription) {
      this.summarySubscription.unsubscribe();
    }
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  loadUserSummary(): void {
    // Subscribe to the summary observable for real-time updates
    this.summarySubscription = this.userSummaryService.summary$.subscribe(summary => {
      this.userSummary = summary;
    });

    // Load initial summary data
    this.userSummaryService.getUserSummary().subscribe({
      next: (summary) => {
        // Summary is automatically updated via the subscription above
      },
      error: (error) => {
        console.error('Failed to load user summary:', error);
        // Don't show error to user as this is supplementary data
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
    this.refreshUserSummary();
  }

  refreshUserSummary(): void {
    this.userSummaryService.refreshSummary();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        this.modalService.success('Успех', 'Токен действителен!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        this.modalService.error('Ошибка', 'Проверка токена не удалась: ' + error.message);
      }
    });
  }

  // Section management (both user and admin sections)
  setActiveSection(section: 'profile' | 'library' | 'purchases' | 'users' | 'games' | 'admin-library' | 'game-keys'): void {
    this.activeSection = section;

    if (section === 'games' && this.games.length === 0) {
      this.loadGames();
    } else if (section === 'users' && this.users.length === 0) {
      this.loadUsers();
    }
  }

  // Games management methods
  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames().subscribe({
      next: (response) => {
        this.games = response.results;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  deleteGame(gameId: number): void {
    this.modalService.confirm(
      'Удаление игры',
      'Вы уверены, что хотите удалить эту игру?',
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.gameService.deleteGame(gameId).subscribe({
          next: () => {
            this.games = this.games.filter(game => game.id !== gameId);
            this.modalService.success('Успех', 'Игра успешно удалена');
          },
          error: (error) => {
            this.modalService.error('Ошибка', 'Не удалось удалить игру: ' + error.message);
          }
        });
      }
    });
  }

  // Users management methods
  loadUsers(): void {
    this.usersLoading = true;
    this.usersError = '';

    const filters = this.buildUserFilters();

    this.userService.getUsers(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.users = response.results;
        this.totalUsers = response.count;
        this.usersLoading = false;
      },
      error: (error) => {
        this.usersError = error.message || 'Failed to load users';
        this.usersLoading = false;
      }
    });
  }

  private buildUserFilters(): UserFilters {
    const filters: UserFilters = {
      ordering: this.sortBy
    };

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.selectedRole !== 'all') {
      if (this.selectedRole === 'staff') {
        filters.is_staff = true;
      } else if (this.selectedRole === 'user') {
        filters.is_staff = false;
      } else if (this.selectedRole === 'superuser') {
        filters.is_superuser = true;
      }
    }

    if (this.selectedStatus !== 'all') {
      filters.is_active = this.selectedStatus === 'active';
    }

    return filters;
  }

  // Search and filter methods
  onSearchChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onRoleFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  // Pagination methods
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUsers();
  }

  get totalPages(): number {
    return Math.ceil(this.totalUsers / this.pageSize);
  }

  get pages(): number[] {
    const pages = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pages.push(i);
    }
    return pages;
  }

  // User management actions
  toggleUserStatus(user: User): void {
    this.userService.toggleUserStatus(user.id, !user.is_active).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
      },
      error: (error) => {
        console.error('Failed to update user status:', error);
        this.modalService.error('Ошибка', 'Не удалось обновить статус пользователя: ' + error.message);
      }
    });
  }

  toggleStaffStatus(user: User): void {
    this.userService.toggleStaffStatus(user.id, !user.is_staff).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
      },
      error: (error) => {
        console.error('Failed to update staff status:', error);
        this.modalService.error('Ошибка', 'Не удалось обновить статус администратора: ' + error.message);
      }
    });
  }

  deleteUser(user: User): void {
    this.modalService.confirm(
      'Удаление пользователя',
      `Вы уверены, что хотите удалить пользователя ${user.email}?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.userService.deleteUser(user.id).subscribe({
          next: () => {
            this.users = this.users.filter(u => u.id !== user.id);
            this.totalUsers--;
          },
          error: (error) => {
            console.error('Failed to delete user:', error);
            this.modalService.error('Ошибка', 'Не удалось удалить пользователя: ' + error.message);
          }
        });
      }
    });
  }

  // Utility methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getUserRoleText(user: User): string {
    if (user.is_superuser) return 'Суперпользователь';
    if (user.is_staff) return 'Администратор';
    return 'Пользователь';
  }

  getUserRoleClass(user: User): string {
    if (user.is_superuser) return 'text-red-400';
    if (user.is_staff) return 'text-green-400';
    return 'text-blue-400';
  }
}
