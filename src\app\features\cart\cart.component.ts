import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../core/services/cart.service';
import { GameService } from '../../core/services/game.service';
import { CheckoutService } from '../../core/services/checkout.service';
import { ModalService } from '../../core/services/modal.service';
import { Cart, CartItem, Purchase } from '../../core/models/cart.model';
import { Game } from '../../core/models/game.model';

@Component({
  selector: 'app-cart',
  standalone: false,
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.css'
})
export class CartComponent implements OnInit, OnDestroy {
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  loading = false;
  error = '';
  checkoutLoading = false;

  // Game data cache
  gameDataCache: { [gameId: number]: Game } = {};
  loadingGameData = false;

  private cartSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private gameService: GameService,
    private checkoutService: CheckoutService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
      this.loadGameDataForCartItems();
    });
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    this.cartService.loadCart().subscribe({
      next: (cart) => {
        this.cart = cart;
        this.loading = false;
        this.loadGameDataForCartItems();
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить корзину';
        this.loading = false;
      }
    });
  }

  private loadGameDataForCartItems(): void {
    if (this.cart.items.length === 0) return;

    this.loadingGameData = true;
    
    // Load game data for items that don't have cached data
    const gameIdsToLoad = this.cart.items
      .filter(item => !this.gameDataCache[item.game])
      .map(item => item.game);

    if (gameIdsToLoad.length === 0) {
      this.loadingGameData = false;
      this.calculateTotals();
      return;
    }

    // Load game data for each unique game ID
    const gameLoadPromises = gameIdsToLoad.map(gameId => 
      this.gameService.getGame(gameId).toPromise()
    );

    Promise.all(gameLoadPromises).then(games => {
      games.forEach(game => {
        if (game) {
          this.gameDataCache[game.id] = game;
        }
      });
      this.loadingGameData = false;
      this.calculateTotals();
    }).catch(error => {
      console.error('Error loading game data:', error);
      this.loadingGameData = false;
    });
  }

  private calculateTotals(): void {
    let totalPrice = 0;
    
    this.cart.items.forEach(item => {
      const gameData = this.gameDataCache[item.game];
      if (gameData) {
        const price = parseFloat(gameData.price) || 0;
        totalPrice += price * item.quantity;
      }
    });

    this.cart.total_price = totalPrice;
  }

  updateQuantity(item: CartItem, newQuantity: number): void {
    if (newQuantity < 1) {
      this.removeItem(item);
      return;
    }

    this.cartService.updateQuantity(item.id, item.game, newQuantity).subscribe({
      next: () => {
        console.log('Quantity updated successfully');
      },
      error: (error) => {
        console.error('Error updating quantity:', error.message);
        this.modalService.error('Ошибка', 'Не удалось обновить количество: ' + error.message);
      }
    });
  }

  removeItem(item: CartItem): void {
    this.modalService.confirm(
      'Удаление из корзины',
      'Вы уверены, что хотите удалить эту игру из корзины?',
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.cartService.removeFromCart(item.id).subscribe({
          next: () => {
            console.log('Item removed successfully');
          },
          error: (error) => {
            console.error('Error removing item:', error.message);
            this.modalService.error('Ошибка', 'Не удалось удалить игру из корзины: ' + error.message);
          }
        });
      }
    });
  }

  clearCart(): void {
    this.modalService.confirm(
      'Очистка корзины',
      'Вы уверены, что хотите удалить все игры из корзины?',
      'Очистить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.cartService.clearCart().subscribe({
          next: () => {
            console.log('Cart cleared successfully');
          },
          error: (error) => {
            console.error('Error clearing cart:', error.message);
            this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
          }
        });
      }
    });
  }

  getGameData(gameId: number): Game | null {
    return this.gameDataCache[gameId] || null;
  }

  getItemTotal(item: CartItem): number {
    const gameData = this.getGameData(item.game);
    if (!gameData) return 0;
    const price = parseFloat(gameData.price) || 0;
    return price * item.quantity;
  }

  checkout(): void {
    if (this.cart.items.length === 0) {
      this.modalService.error('Ошибка', 'Корзина пуста');
      return;
    }

    this.modalService.confirm(
      'Подтверждение покупки',
      `Вы уверены, что хотите купить ${this.cart.total_items} ${this.cart.total_items === 1 ? 'игру' : this.cart.total_items < 5 ? 'игры' : 'игр'} на сумму ${this.formatPrice(this.cart.total_price)}?`,
      'Купить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.processCheckout();
      }
    });
  }

  private processCheckout(): void {
    this.checkoutLoading = true;

    this.checkoutService.checkout().subscribe({
      next: (purchases: Purchase[]) => {
        this.checkoutLoading = false;

        // Show success message
        const purchaseCount = purchases.length;
        const successMessage = `Успешно приобретено ${purchaseCount} ${purchaseCount === 1 ? 'игра' : purchaseCount < 5 ? 'игры' : 'игр'}!`;

        this.modalService.success('Покупки созданы', successMessage + ' Перейдите в историю покупок для оплаты.').then(() => {
          // Navigate to purchase history in profile
          this.router.navigate(['/profile'], { fragment: 'purchases' });
        });
      },
      error: (error) => {
        this.checkoutLoading = false;
        console.error('Checkout error:', error.message);
        this.modalService.error('Ошибка покупки', 'Не удалось завершить покупку: ' + error.message);
      }
    });
  }

  formatPrice(price: string | number): string {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  goToGames(): void {
    this.router.navigate(['/games']);
  }

  viewGameDetails(gameId: number): void {
    this.router.navigate(['/games', gameId]);
  }
}
