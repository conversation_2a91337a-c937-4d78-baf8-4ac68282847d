import { Component, OnInit } from '@angular/core';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { Game, GameFilters, CreateGameRequest } from '../../../../core/models/game.model';
import { debounceTime, Subject } from 'rxjs';

@Component({
  selector: 'app-games-management',
  standalone: false,
  templateUrl: './games-management.component.html',
  styleUrl: './games-management.component.css'
})
export class GamesManagementComponent implements OnInit {
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Pagination
  totalGames = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-created_at';
  private searchSubject = new Subject<string>();

  // Add game form
  showAddForm = false;
  addGameLoading = false;
  addGameError = '';
  newGame: CreateGameRequest = {
    title: '',
    description: '',
    price: '',
    trial_available: false,
    requires_device: false
  };
  selectedCoverImage: File | null = null;

  // Game detail modal
  selectedGameId: number | null = null;
  showGameDetail = false;

  constructor(
    private gameService: GameService,
    private modalService: ModalService
  ) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.loadGames();
    });
  }

  ngOnInit(): void {
    this.loadGames();
  }

  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    const filters: GameFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.gameService.getGames(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.games = response.results;
        this.totalGames = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.loadGames();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.sortBy = '-created_at';
    this.currentPage = 1;
    this.loadGames();
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.loadGames();
    }
  }

  nextPage(): void {
    if (this.hasNext) {
      this.currentPage++;
      this.loadGames();
    }
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.currentPage--;
      this.loadGames();
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalGames / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Add game form methods
  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (!this.showAddForm) {
      this.resetAddForm();
    }
  }

  resetAddForm(): void {
    this.newGame = {
      title: '',
      description: '',
      price: '',
      trial_available: false,
      requires_device: false
    };
    this.selectedCoverImage = null;
    this.addGameError = '';
  }

  // Game detail modal methods
  openGameDetail(gameId: number): void {
    this.selectedGameId = gameId;
    this.showGameDetail = true;
  }

  closeGameDetail(): void {
    this.selectedGameId = null;
    this.showGameDetail = false;
  }

  onGameUpdated(updatedGame: Game): void {
    // Update the game in the local array
    const index = this.games.findIndex(game => game.id === updatedGame.id);
    if (index !== -1) {
      this.games[index] = updatedGame;
    }
  }

  onGameDeleted(gameId: number): void {
    // Remove the game from the local array
    this.games = this.games.filter(game => game.id !== gameId);
    this.totalGames--;

    // If this was the last game on the current page and we're not on page 1, go to previous page
    if (this.games.length === 0 && this.currentPage > 1) {
      this.currentPage--;
      this.loadGames();
    }
  }

  onCoverImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.addGameError = 'Пожалуйста, выберите файл изображения';
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.addGameError = 'Размер файла не должен превышать 5MB';
        return;
      }

      this.selectedCoverImage = file;
      this.addGameError = '';
    }
  }

  submitAddGame(): void {
    // Validate required fields
    if (!this.newGame.title.trim()) {
      this.addGameError = 'Название игры обязательно';
      return;
    }

    if (!this.newGame.description.trim()) {
      this.addGameError = 'Описание игры обязательно';
      return;
    }

    // Validate price format
    if (!this.newGame.price || this.newGame.price.trim() === '') {
      this.addGameError = 'Цена игры обязательна';
      return;
    }

    const priceNum = parseFloat(this.newGame.price);
    if (isNaN(priceNum) || priceNum < 0) {
      this.addGameError = 'Введите корректную цену (должна быть больше или равна 0)';
      return;
    }

    this.addGameLoading = true;
    this.addGameError = '';

    // Prepare game data
    const gameData: CreateGameRequest = {
      ...this.newGame,
      price: priceNum.toFixed(2)
    };

    // Add cover image if selected
    if (this.selectedCoverImage) {
      gameData.cover_image = this.selectedCoverImage;
    }

    this.gameService.createGame(gameData).subscribe({
      next: (createdGame) => {
        this.addGameLoading = false;
        this.showAddForm = false;
        this.resetAddForm();

        // Refresh the games list
        this.currentPage = 1; // Go to first page to see the new game
        this.loadGames();

        this.modalService.success('Успех', 'Игра успешно создана!');
      },
      error: (error) => {
        this.addGameLoading = false;

        let errorMessage = '';

        // Handle validation errors
        if (error.error && typeof error.error === 'object') {
          const errors = error.error;
          if (errors.title) {
            errorMessage = 'Название: ' + errors.title.join(', ');
          } else if (errors.description) {
            errorMessage = 'Описание: ' + errors.description.join(', ');
          } else if (errors.price) {
            errorMessage = 'Цена: ' + errors.price.join(', ');
          } else if (errors.cover_image) {
            errorMessage = 'Изображение: ' + errors.cover_image.join(', ');
          } else if (errors.non_field_errors) {
            errorMessage = errors.non_field_errors.join(', ');
          } else {
            errorMessage = 'Ошибка при создании игры';
          }
        } else {
          errorMessage = error.message || 'Ошибка при создании игры';
        }

        // Set inline error for form display
        this.addGameError = errorMessage;

        // Also show beautiful modal error
        this.modalService.error('Ошибка создания игры', errorMessage);
      }
    });
  }


}
